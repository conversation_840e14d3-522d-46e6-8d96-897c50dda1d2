import React from 'react';

const Logo = ({ className = "h-8 w-auto", showText = true, variant = "default" }) => {
  const logoVariants = {
    default: "text-gray-700",
    white: "text-white",
    dark: "text-gray-900"
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Logo SVG */}
      <svg 
        width="40" 
        height="32" 
        viewBox="0 0 120 80" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className="flex-shrink-0"
      >
        {/* Heart shape */}
        <path 
          d="M85 25C85 15 92 8 102 8C112 8 119 15 119 25C119 35 102 52 102 52S85 35 85 25Z" 
          fill="#F87171" 
          opacity="0.8"
        />
        <path 
          d="M68 25C68 15 75 8 85 8C95 8 102 15 102 25C102 35 85 52 85 52S68 35 68 25Z" 
          fill="#F87171" 
          opacity="0.6"
        />
        
        {/* Curved "L" shape */}
        <path 
          d="M15 15C15 15 15 45 15 55C15 65 25 75 35 75C45 75 55 75 55 75" 
          stroke="#6B7280" 
          strokeWidth="8" 
          strokeLinecap="round" 
          fill="none"
        />
        
        {/* Text in SVG */}
        <text 
          x="15" 
          y="35" 
          fontFamily="Arial, sans-serif" 
          fontSize="8" 
          fontWeight="bold" 
          fill="currentColor"
          className={logoVariants[variant]}
        >
          LIVING
        </text>
        <text 
          x="15" 
          y="45" 
          fontFamily="Arial, sans-serif" 
          fontSize="8" 
          fontWeight="bold" 
          fill="currentColor"
          className={logoVariants[variant]}
        >
          ONCOLOGY
        </text>
      </svg>
      
      {/* Optional text beside logo */}
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold text-lg leading-tight ${logoVariants[variant]}`}>
            Living Oncology
          </span>
          <span className={`text-xs opacity-75 ${logoVariants[variant]}`}>
            Supporting Brain Cancer Patients
          </span>
        </div>
      )}
    </div>
  );
};

export default Logo;
