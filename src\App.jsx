import React from 'react';

function App() {
  console.log('App component is rendering!');

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: 'red', fontSize: '32px', marginBottom: '20px' }}>
        🚨 BASIC TEST - Can you see this?
      </h1>
      <p style={{ color: 'blue', fontSize: '18px', marginBottom: '15px' }}>
        If you can see this text, React is working properly.
      </p>
      <div style={{
        backgroundColor: 'yellow',
        padding: '15px',
        margin: '15px 0',
        border: '2px solid orange',
        borderRadius: '5px'
      }}>
        🟡 Yellow box test - This should be visible
      </div>
      <button
        style={{
          backgroundColor: 'green',
          color: 'white',
          padding: '15px 20px',
          border: 'none',
          borderRadius: '5px',
          fontSize: '16px',
          cursor: 'pointer',
          marginRight: '10px'
        }}
        onClick={() => alert('Button clicked! JavaScript is working!')}
      >
        🟢 Click me to test JavaScript
      </button>
      <div style={{
        marginTop: '20px',
        padding: '10px',
        backgroundColor: 'lightblue',
        borderRadius: '5px'
      }}>
        <strong>Debug Info:</strong>
        <br />
        • React: {React.version || 'Unknown'}
        <br />
        • Current URL: {window.location.href}
        <br />
        • Time: {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
}

export default App;
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider as SupabaseAuthProvider } from '@/contexts/SupabaseAuthContext';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import Home from '@/pages/Home';
import AboutUs from '@/pages/AboutUs';
import AboutDrGatson from '@/pages/AboutDrGatson';
import BrainStormCancer from '@/pages/BrainStormCancer';
import EventDetail from '@/pages/EventDetail';
import OncologyConversations from '@/pages/OncologyConversations';
import Testimonials from '@/pages/Testimonials';
import PhotoGallery from '@/pages/PhotoGallery';
import Contact from '@/pages/Contact';
import Services from '@/pages/Services';
import Resources from '@/pages/Resources';
import Community from '@/pages/Community';
import MembersDirectory from '@/pages/MembersDirectory';
import Donate from '@/pages/Donate';
import EducationalHub from '@/pages/EducationalHub';
import SignUp from '@/pages/SignUp';
import Login from '@/pages/Login';
import Profile from '@/pages/Profile';
import ProtectedRoute from '@/components/ProtectedRoute';
import AdminRoute from '@/components/AdminRoute';
import AdminDashboard from '@/pages/admin/AdminDashboard';
import TestComponent from '@/components/TestComponent';
import SimpleHome from '@/components/SimpleHome';

// Trigger deployment
function App() {
  return (
    <Router>
      <div className="min-h-screen flex flex-col">
        <Helmet>
          <title>Living Oncology – Cancer Health Literacy & Support</title>
          <meta name="description" content="Living Oncology is a 501(c)(3) charitable nonprofit organization improving health literacy for cancer patients, caregivers, and professionals. LIVING is larger than Life." />
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
          <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Playfair+Display:wght@600;700&display=swap" rel="stylesheet" />
        </Helmet>

        <a href="#main-content" className="skip-link">
          Skip to main content
        </a>

        <Navigation />

        <main id="main-content" className="flex-grow">
            <Routes>
              <Route path="/test" element={<TestComponent />} />
              <Route path="/simple" element={<SimpleHome />} />
              <Route path="/LO-Website/" element={<SimpleHome />} />
              <Route path="/" element={<SimpleHome />} />
              <Route path="/LO-Website/about-us" element={<AboutUs />} />
              <Route path="/about-us" element={<AboutUs />} />
              <Route path="/LO-Website/about-dr-gatson" element={<AboutDrGatson />} />
              <Route path="/about-dr-gatson" element={<AboutDrGatson />} />
              <Route path="/LO-Website/brainstorm-cancer" element={<BrainStormCancer />} />
              <Route path="/brainstorm-cancer" element={<BrainStormCancer />} />
              <Route path="/LO-Website/brainstorm-cancer/:eventId" element={<EventDetail />} />
              <Route path="/brainstorm-cancer/:eventId" element={<EventDetail />} />
              <Route path="/LO-Website/oncology-conversations" element={<OncologyConversations />} />
              <Route path="/oncology-conversations" element={<OncologyConversations />} />
              <Route path="/LO-Website/educational-hub" element={<EducationalHub />} />
              <Route path="/educational-hub" element={<EducationalHub />} />
              <Route path="/LO-Website/testimonials" element={<Testimonials />} />
              <Route path="/testimonials" element={<Testimonials />} />
              <Route path="/LO-Website/photo-gallery" element={<PhotoGallery />} />
              <Route path="/photo-gallery" element={<PhotoGallery />} />
              <Route path="/LO-Website/contact" element={<Contact />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/LO-Website/services" element={<Services />} />
              <Route path="/services" element={<Services />} />
              <Route path="/LO-Website/resources" element={<Resources />} />
              <Route path="/resources" element={<Resources />} />
              <Route path="/LO-Website/community" element={<Community />} />
              <Route path="/community" element={<Community />} />
              <Route path="/LO-Website/members-directory" element={<MembersDirectory />} />
              <Route path="/members-directory" element={<MembersDirectory />} />
              <Route path="/LO-Website/donate" element={<Donate />} />
              <Route path="/donate" element={<Donate />} />
              <Route path="/LO-Website/signup" element={<SignUp />} />
              <Route path="/signup" element={<SignUp />} />
              <Route path="/LO-Website/login" element={<Login />} />
              <Route path="/login" element={<Login />} />
              <Route path="/LO-Website/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
              <Route path="/LO-Website/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
              <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
            </Routes>
          </main>
          
          <Footer />
          <Toaster />
        </div>
      </Router>
  );
}

export default App;