import React from 'react';

const HeroLogo = ({ className = "w-full max-w-md mx-auto" }) => {

  return (
    <div className={className}>
      <svg
        width="400"
        height="300"
        viewBox="0 0 400 300"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-auto"
      >
        {/* Gradient Definitions */}
        <defs>
          <radialGradient id="heroHeartGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#FF6B9D" />
            <stop offset="30%" stopColor="#F87171" />
            <stop offset="70%" stopColor="#EF4444" />
            <stop offset="100%" stopColor="#DC2626" />
          </radialGradient>

          <linearGradient id="heroCurveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#6366F1" />
            <stop offset="30%" stopColor="#8B5CF6" />
            <stop offset="70%" stopColor="#A855F7" />
            <stop offset="100%" stopColor="#C084FC" />
          </linearGradient>
        </defs>

        {/* Heart shapes */}
        <path
          d="M280 80C280 50 300 30 330 30C360 30 380 50 380 80C380 110 330 170 330 170S280 110 280 80Z"
          fill="url(#heroHeartGradient)"
          opacity="0.95"
        />
        <path
          d="M230 80C230 50 250 30 280 30C310 30 330 50 330 80C330 110 280 170 280 170S230 110 230 80Z"
          fill="url(#heroHeartGradient)"
          opacity="0.8"
        />

        {/* Curved "L" shape */}
        <path
          d="M50 50C50 50 50 150 50 180C50 210 70 230 100 230C130 230 180 230 180 230"
          stroke="url(#heroCurveGradient)"
          strokeWidth="12"
          strokeLinecap="round"
          fill="none"
        />

        {/* Text */}
        <text
          x="50"
          y="120"
          fontFamily="Inter, Arial, sans-serif"
          fontSize="28"
          fontWeight="800"
          fill="#374151"
          letterSpacing="2px"
        >
          LIVING
        </text>
        <text
          x="50"
          y="155"
          fontFamily="Inter, Arial, sans-serif"
          fontSize="28"
          fontWeight="800"
          fill="#374151"
          letterSpacing="2px"
        >
          ONCOLOGY
        </text>

        {/* Subtitle */}
        <text
          x="50"
          y="185"
          fontFamily="Inter, Arial, sans-serif"
          fontSize="14"
          fontWeight="500"
          fill="#6B7280"
          letterSpacing="1px"
          opacity="0.8"
        >
          Supporting Brain Cancer Patients
        </text>
      </svg>
    </div>
  );
};

export default HeroLogo;
